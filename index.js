require('dotenv').config();
const { Client, GatewayIntentBits, Partials } = require('discord.js');
const config = require('./config/config.js');
const fs = require('fs');
const CommandHandler = require('./src/utils/commandHandler');
const chalk = require('chalk');
const express = require('express');
const app = express();
const PORT = process.env.PORT || 3000;

// Simple health check endpoint
app.get('/', (req, res) => {
    res.send('Bot is running!');
});

app.listen(PORT, () => {
    console.log(`Server is running on port ${PORT}`);
});
// Import database connection
const databaseConnection = require('./config/database');

// Import new configurable modules
const ConfigurableInfernalMonitor = require('./src/modules/configurableInfernal');
const BotInfernalMonitor = require('./src/monitors/infernal'); // New bot-integrated infernal system
const AutoPublisher = require('./src/modules/autoPublisher');
const JoinRequestHandler = require('./src/modules/joinRequestHandler');
const ScreenshotVerification = require('./src/modules/screenshotVerification');
const DungeonAlert = require('./src/modules/dungeonAlert');
const NewHunterKeyMonitor = require('./src/modules/newHunterKey'); // New hunter key monitor
const DesertFarmMonitor = require('./src/modules/desertFarm'); // Desert farm monitor
const WorldBossAlert = require('./src/modules/worldBossAlert'); // World boss alert system
const AdvancedAutomod = require('./src/modules/automod');
const ServerManager = require('./src/modules/serverManager'); // Server management system

// Import dungeon monitor (this starts the dungeon monitoring automatically)
require('./src/monitors/dungeons.js');
// Import world boss monitor (this starts the world boss monitoring automatically)
require('./src/monitors/worldboss2.js');

// Initialize the bot client with the required intents
const client = new Client({
    intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildMessages,
        GatewayIntentBits.MessageContent,
        GatewayIntentBits.GuildMembers,
        GatewayIntentBits.GuildMessageReactions,
    ],
    partials: [
      Partials.Message,
      Partials.Channel,
      Partials.Reaction
    ] 
});

// Load commands and slash commands
new CommandHandler(client);

// Load event files and set event handlers
const eventFiles = fs.readdirSync('./src/events').filter(file => file.endsWith('.js'));
for (const file of eventFiles) {
    const event = require(`./src/events/${file}`);
    if (event.once) {
        client.once(event.name, (...args) => event.execute(...args));
    } else {
        client.on(event.name, (...args) => event.execute(...args));
    }
}

// Initialize modules
const configurableInfernal = new ConfigurableInfernalMonitor();
const botInfernal = new BotInfernalMonitor(client); // New bot-integrated infernal system
const autoPublisher = new AutoPublisher(client);
const joinRequestHandler = new JoinRequestHandler(client);
const screenshotVerification = new ScreenshotVerification(client);
const dungeonAlert = new DungeonAlert(client);
const worldBossAlert = new WorldBossAlert(client); // World boss alert system
const newHunterKey = new NewHunterKeyMonitor(client); // New hunter key monitor
const desertFarm = new DesertFarmMonitor(client); // Desert farm monitor
const automod = new AdvancedAutomod(client);
const serverManager = new ServerManager(client); // Server management system

// Event handlers
client.once('ready', async () => {
    console.log(`Logged in as ${client.user.tag}`);
    console.log(chalk.green('=== Initializing Database ==='));

    // Initialize database connection
    try {
        await databaseConnection.connect();
        console.log(chalk.green('✅ Database connection established'));
    } catch (error) {
        console.error(chalk.red('❌ Failed to connect to database:'), error);
        console.log(chalk.yellow('⚠️ Bot will continue without database (using file-based config)'));
    }

    console.log(chalk.green('=== Initializing Modules ==='));

    // Initialize all modules
    configurableInfernal.initialize();
    botInfernal.initialize(); // New bot-integrated infernal system
    autoPublisher.initialize();
    joinRequestHandler.initialize();
    screenshotVerification.initialize();
    dungeonAlert.initialize();
    worldBossAlert.initialize();
    newHunterKey.initialize();
    desertFarm.initialize();
    automod.initialize();
    serverManager.initialize();

    // Make modules accessible from client for slash commands
    client.automod = automod;
    client.botInfernal = botInfernal; // New bot-integrated infernal system
    client.configurableInfernal = configurableInfernal;
    client.newHunterKey = newHunterKey;
    client.desertFarm = desertFarm;
    client.serverManager = serverManager;

    console.log(chalk.green('✅ Bot-integrated infernal system initialized'));

    console.log(chalk.green('=== All Modules Initialized ==='));
});

// Handle server join events
client.on('guildCreate', async (guild) => {
    try {
        console.log(chalk.green(`[Guild Join] Joined server: ${guild.name} (${guild.id}) - ${guild.memberCount} members`));

        // Log the server join
        if (serverManager) {
            await serverManager.logServerJoin(guild);
            
            // Immediately check if server meets minimum member requirement
            await serverManager.checkServerOnJoin(guild);
        }

    } catch (error) {
        console.error(chalk.red('[Guild Join] Error handling server join:'), error);
    }
});

// Handle server leave events
client.on('guildDelete', async (guild) => {
    try {
        console.log(chalk.yellow(`[Guild Leave] Left server: ${guild.name} (${guild.id})`));

        // Log the server leave (but don't delete configuration)
        if (serverManager) {
            await serverManager.handleServerLeave(guild, false);
        }

    } catch (error) {
        console.error(chalk.red('[Guild Leave] Error handling server leave:'), error);
    }
});

// Graceful shutdown
process.on('SIGINT', () => {
    console.log('Received SIGINT, shutting down gracefully...');

    // Shutdown all modules
    configurableInfernal.shutdown();
    botInfernal.shutdown(); // New bot-integrated infernal system
    worldBossAlert.shutdown();
    newHunterKey.shutdown();
    desertFarm.shutdown();
    serverManager.shutdown();
    process.exit(0);
});

process.on('SIGTERM', async () => {
    console.log('Received SIGTERM, shutting down gracefully...');

    // Shutdown all modules
    configurableInfernal.shutdown();
    botInfernal.shutdown(); // New bot-integrated infernal system
    worldBossAlert.shutdown();
    newHunterKey.shutdown();
    desertFarm.shutdown();
    serverManager.shutdown();

    // Close database connection
    try {
        await databaseConnection.disconnect();
        console.log(chalk.green('✅ Database connection closed'));
    } catch (error) {
        console.error(chalk.red('❌ Error closing database connection:'), error);
    }

    process.exit(0);
});

// Log in to Discord
client.login(config.token).catch(error => {
    const logStatus = require('./events/ready');
    logStatus.execute(client, error); // Pass the error to the logging function
});

module.exports = client;