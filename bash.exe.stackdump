Stack trace:
Frame         Function      Args
0007FFFF35C0  00021006116E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF24C0) msys-2.0.dll+0x2116E
0007FFFF35C0  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFF35C0  0002100469F2 (00021028DF99, 0007FFFF3478, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFF35C0  00021006A3FE (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A3FE
0007FFFF35C0  00021006A525 (0007FFFF35D0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A525
0001004F94B7  00021006B985 (0007FFFF35D0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B985
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFEF8F70000 ntdll.dll
7FFEF7AC0000 KERNEL32.DLL
7FFEF63C0000 KERNELBASE.dll
7FFEF87D0000 USER32.dll
7FFEF6390000 win32u.dll
7FFEF7180000 GDI32.dll
7FFEF6AD0000 gdi32full.dll
7FFEF6860000 msvcp_win.dll
7FFEF6980000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFEF8D90000 advapi32.dll
7FFEF8CE0000 msvcrt.dll
7FFEF8990000 sechost.dll
7FFEF6AA0000 bcrypt.dll
7FFEF71B0000 RPCRT4.dll
7FFEF5870000 CRYPTBASE.DLL
7FFEF61D0000 bcryptPrimitives.dll
7FFEF7770000 IMM32.DLL
