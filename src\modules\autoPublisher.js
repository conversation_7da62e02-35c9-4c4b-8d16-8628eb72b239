const { Events, ChannelType } = require('discord.js');
const configService = require('../services/configService');
const chalk = require('chalk');

class AutoPublisher {
    constructor(client) {
        this.client = client;
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Listen for messages in announcement channels
        this.client.on(Events.MessageCreate, async (message) => {
            await this.handleMessage(message);
        });

        console.log(chalk.blue('📢 AutoPublisher module initialized'));
    }

    // Initialize method for compatibility with index.js
    initialize() {
        console.log(chalk.blue('📢 AutoPublisher initialized and ready'));
    }

    async handleMessage(message) {
        try {
            // Skip if not in a guild
            if (!message.guild) return;

            // Skip if not an announcement channel
            if (message.channel.type !== ChannelType.GuildAnnouncement) return;

            // Get server configuration
            const autopublisherConfig = await configService.getAutopublisherConfig(message.guild.id);
            
            // Skip if autopublisher is not enabled for this server
            if (!autopublisherConfig?.enabled) return;

            // Check if this channel is configured for autopublishing
            const channelConfig = autopublisherConfig.channels?.[message.channel.id];
            if (!channelConfig?.enabled) return;

            // Check if bot has permission to manage messages in this channel
            if (!message.channel.permissionsFor(message.guild.members.me)?.has('ManageMessages')) {
                console.warn(chalk.yellow(`⚠️ Missing ManageMessages permission in ${message.channel.name} (${message.guild.name})`));
                return;
            }

            // Wait 5 seconds before publishing the message
            console.log(chalk.cyan(`⏳ Waiting 5 seconds before auto-publishing message in ${message.channel.name} (${message.guild.name})`));
            await new Promise(resolve => setTimeout(resolve, 5000));

            // Publish the message
            try {
                await message.crosspost();
                console.log(chalk.green(`📢 Auto-published message in ${message.channel.name} (${message.guild.name}) from ${message.author.bot ? 'bot' : 'user'}: ${message.author.tag}`));
            } catch (publishError) {
                // Handle specific publish errors
                if (publishError.code === 50013) {
                    console.warn(chalk.yellow(`⚠️ Missing permissions to publish in ${message.channel.name} (${message.guild.name})`));
                } else if (publishError.code === 50033) {
                    console.warn(chalk.yellow(`⚠️ Message already published in ${message.channel.name} (${message.guild.name})`));
                } else if (publishError.code === 50035) {
                    console.warn(chalk.yellow(`⚠️ Cannot publish system message in ${message.channel.name} (${message.guild.name})`));
                } else {
                    console.error(chalk.red(`❌ Error publishing message in ${message.channel.name} (${message.guild.name}):`), publishError);
                }
            }

        } catch (error) {
            console.error(chalk.red('❌ Error in AutoPublisher handleMessage:'), error);
        }
    }

    // Method to get autopublisher statistics
    async getStats() {
        try {
            const enabledServers = await configService.getEnabledAutopublisherServers();
            
            let totalChannels = 0;
            let enabledChannels = 0;
            
            for (const server of enabledServers) {
                const channels = server.config?.channels || {};
                totalChannels += Object.keys(channels).length;
                enabledChannels += Object.values(channels).filter(ch => ch.enabled).length;
            }

            return {
                enabledServers: enabledServers.length,
                totalChannels,
                enabledChannels
            };
        } catch (error) {
            console.error(chalk.red('❌ Error getting autopublisher stats:'), error);
            return {
                enabledServers: 0,
                totalChannels: 0,
                enabledChannels: 0
            };
        }
    }

    // Method to validate channel configurations
    async validateConfigurations() {
        try {
            const enabledServers = await configService.getEnabledAutopublisherServers();
            const issues = [];

            for (const serverConfig of enabledServers) {
                const guild = this.client.guilds.cache.get(serverConfig.serverId);
                if (!guild) {
                    issues.push({
                        serverId: serverConfig.serverId,
                        serverName: serverConfig.name,
                        issue: 'Bot not in server'
                    });
                    continue;
                }

                const channels = serverConfig.config?.channels || {};
                for (const [channelId, channelConfig] of Object.entries(channels)) {
                    const channel = guild.channels.cache.get(channelId);
                    
                    if (!channel) {
                        issues.push({
                            serverId: serverConfig.serverId,
                            serverName: serverConfig.name,
                            channelId,
                            issue: 'Channel not found'
                        });
                        continue;
                    }

                    if (channel.type !== ChannelType.GuildAnnouncement) { // Not an announcement channel
                        issues.push({
                            serverId: serverConfig.serverId,
                            serverName: serverConfig.name,
                            channelId,
                            channelName: channel.name,
                            issue: 'Not an announcement channel'
                        });
                        continue;
                    }

                    if (!channel.permissionsFor(guild.members.me)?.has('ManageMessages')) {
                        issues.push({
                            serverId: serverConfig.serverId,
                            serverName: serverConfig.name,
                            channelId,
                            channelName: channel.name,
                            issue: 'Missing ManageMessages permission'
                        });
                    }
                }
            }

            return issues;
        } catch (error) {
            console.error(chalk.red('❌ Error validating autopublisher configurations:'), error);
            return [];
        }
    }

    // Method to cleanup invalid configurations
    async cleanupInvalidConfigurations() {
        try {
            const issues = await this.validateConfigurations();
            const cleanupResults = {
                serversProcessed: 0,
                channelsRemoved: 0,
                serversDisabled: 0
            };

            // Group issues by server
            const serverIssues = {};
            for (const issue of issues) {
                if (!serverIssues[issue.serverId]) {
                    serverIssues[issue.serverId] = [];
                }
                serverIssues[issue.serverId].push(issue);
            }

            for (const [serverId, issues] of Object.entries(serverIssues)) {
                cleanupResults.serversProcessed++;
                
                // Check if bot is not in server - disable entire autopublisher
                const botNotInServer = issues.some(issue => issue.issue === 'Bot not in server');
                if (botNotInServer) {
                    // Disable autopublisher for this server
                    const config = await configService.getServerConfig(serverId);
                    if (config?.autopublisher) {
                        config.autopublisher.enabled = false;
                        await configService.saveServerConfig(serverId, config);
                        cleanupResults.serversDisabled++;
                        console.log(chalk.yellow(`⚠️ Disabled autopublisher for server ${serverId} (bot not in server)`));
                    }
                    continue;
                }

                // Remove invalid channels
                const config = await configService.getServerConfig(serverId);
                if (config?.autopublisher?.channels) {
                    for (const issue of issues) {
                        if (issue.channelId && config.autopublisher.channels[issue.channelId]) {
                            delete config.autopublisher.channels[issue.channelId];
                            cleanupResults.channelsRemoved++;
                            console.log(chalk.yellow(`⚠️ Removed invalid channel ${issue.channelId} from server ${serverId}: ${issue.issue}`));
                        }
                    }
                    
                    // Save updated configuration
                    await configService.saveServerConfig(serverId, config);
                }
            }

            console.log(chalk.blue(`🧹 Autopublisher cleanup completed: ${cleanupResults.channelsRemoved} channels removed, ${cleanupResults.serversDisabled} servers disabled`));
            return cleanupResults;
        } catch (error) {
            console.error(chalk.red('❌ Error cleaning up autopublisher configurations:'), error);
            return {
                serversProcessed: 0,
                channelsRemoved: 0,
                serversDisabled: 0
            };
        }
    }
}

module.exports = AutoPublisher;